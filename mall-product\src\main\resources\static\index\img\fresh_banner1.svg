<svg width="590" height="470" viewBox="0 0 590 470" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bg1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E8F5E8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C8E6C9;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="590" height="470" fill="url(#bg1)"/>
  
  <!-- 水果图案 -->
  <circle cx="150" cy="150" r="40" fill="#FF6B6B"/>
  <path d="M140 140 Q150 130 160 140" stroke="#4CAF50" stroke-width="3" fill="none"/>
  <text x="130" y="220" font-family="Arial" font-size="16" fill="#2E7D32">新鲜苹果</text>
  
  <circle cx="300" cy="180" r="35" fill="#FFD54F"/>
  <path d="M290 170 Q300 160 310 170" stroke="#4CAF50" stroke-width="3" fill="none"/>
  <text x="280" y="250" font-family="Arial" font-size="16" fill="#2E7D32">香甜橙子</text>
  
  <ellipse cx="450" cy="160" rx="30" ry="40" fill="#9C27B0"/>
  <path d="M440 150 Q450 140 460 150" stroke="#4CAF50" stroke-width="3" fill="none"/>
  <text x="430" y="230" font-family="Arial" font-size="16" fill="#2E7D32">紫色葡萄</text>
  
  <!-- 主标题 -->
  <text x="50" y="350" font-family="Arial" font-size="36" font-weight="bold" fill="#2E7D32">新鲜水果</text>
  <text x="50" y="390" font-family="Arial" font-size="24" fill="#388E3C">每日直达 品质保证</text>
  
  <!-- 装饰叶子 -->
  <path d="M500 300 Q520 280 540 300 Q520 320 500 300" fill="#4CAF50" opacity="0.7"/>
  <path d="M510 320 Q530 300 550 320 Q530 340 510 320" fill="#66BB6A" opacity="0.7"/>
</svg>
