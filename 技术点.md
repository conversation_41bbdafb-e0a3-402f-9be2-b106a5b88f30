## 分布式基础篇


## 分布式高级篇
- 商品三级分类信息 锁和缓存
- 商品列表 elasticsearch 
- 商品详情 JUC 任务异步编排 
- 微博社交登录
- 离线购物车(SpringSession)
- ThreadLocal保存线程变量
- 订单 rabbitMQ

RabbitMQ消息确认机制
![](https://pic.vbean.eu.org/images/2024/03/5b7fe48f7a61962307cd982102f6e49c.png)

```docker run -d -p 9411:9411 openzipkin/zipkin:2.10.0```

## 集群高可用篇
- [kubernets](https://kubernetes.io/zh-cn/docs/setup/production-environment/)

-Xms512m -Xmx512m -Xss256k
-Xms100m -Xmx100m -Xss256k
-Xmx 最大堆内存
-Xms 初始堆内存
-Xmn 年轻代大小(新生代)
-Xss 每个线程栈大小

登录账户: admin
密码: admin
启动renren-fast后台:
```shell
su kevin
cd /device/ubuntu/gulimall-admin-vue/gulimall-admin-vue-app
nvm use 10.16.3
npm run dev
```
获取样例数据：
https://www.10mart.com.tw/pc

https://ph.abean.eu.org/file/AgACAgUAAyEGAASq5DuXAAMdaQGJyLfDBAPbRASvQQ2et0FEq-kAAm0Laxs-AAEJVM0RC-rgtf76AQADAgADeQADNgQ.png
## 部署启动顺序
- nacos redis rabbitmq elasticsearch
- product 命令： nohup java -jar -server -Xms512m -Xmx512m -Xss256k mall-product-*.jar --server.port=10000 > product-service.log 2>&1 &
- auth 命令： nohup java -jar -server -Xms100m -Xmx100m -Xss256k mall-auth-0.0.1-SNAPSHOT.jar --server.port=20000  > auth-service.log 2>&1 &
- member 命令： nohup java -jar -server -Xms100m -Xmx100m -Xss256k mall-member-0.0.1-SNAPSHOT.jar --server.port=8000 > member-service.log 2>&1 &
- third-party 命令： nohup java -jar -server -Xms100m -Xmx100m -Xss256k mall-third-party-0.0.1-SNAPSHOT.jar --server.port=8080 > third-party-service.log 2>&1 &
- coupon 命令： nohup java -jar -server -Xms100m -Xmx100m -Xss256k mall-coupon-0.0.1-SNAPSHOT.jar --server.port=30080 > coupon-service.log 2>&1 &
- gateway 102机器命令： nohup java -jar -server -Xms512m -Xmx512m -Xss256k mall-gateway-*.jar --server.port=8888 > gateway-service.log 2>&1 &
- renren-fast后台 命令： nohup java -jar -server -Xms512m -Xmx512m -Xss256k renren-fast.jar --server.port=8060 > renren-fast-service.log 2>&1 &
- order 命令: nohup java -jar -server -Xms512m -Xmx512m -Xss256k mall-order-0.0.1-SNAPSHOT.jar --server.port=8050 > order-service.log 2>&1 &
- search 102命令： nohup java -jar -server -Xms512m -Xmx512m -Xss256k mall-search-0.0.1-SNAPSHOT.jar --server.port=8040  > search-service.log 2>&1 &
- seckill 102命令： nohup java -jar -server -Xms512m -Xmx512m -Xss256k mall-seckill-0.0.1-SNAPSHOT.jar --server.port=25000 > seckill-service.log 2>&1 &
- ware 102命令： nohup java -jar -server -Xms512m -Xmx512m -Xss256k mall-ware-0.0.1-SNAPSHOT.jar --server.port=8070 > ware-service.log 2>&1 &
- cart 102命令： nohup java -jar -server -Xms512m -Xmx512m -Xss256k mall-cart-0.0.1-SNAPSHOT.jar --server.port=30010 > cart-service.log 2>&1 &
- sentinel-dashboard 102命令： nohup java -jar -server -Xms512m -Xmx512m -Xss256k sentinel-dashboard-1.7.1.jar --server.port=8333 > sentinel-dashboard-service.log 2>&1 &


102：
gateway
member
order
product
ware
search
cart

100：
third-party
auth
renren-fast
coupon


分布式锁应用：
定时扫描数据库中秒杀商品到redis缓存中

## 消息队列 RabbitMQ
fanout 广播模式
topic 主题模式(部分广播)


http://104.199.179.169:15672/#/exchanges
35.201.131.252
104.199.179.169

支付宝沙箱账号：
<EMAIL>
111111
支付密码
111111
分布式锁应用：
在上架每日秒杀商品定时任务中 使用分布式锁 避免多实例同时执行定时任务 导致重复扫描数据

延迟队列的原理：
![](https://cdn.abean.eu.org/2025/11/f0711914744666a98d12eb482f5ae9ca.png)
![](https://cdn.abean.eu.org/2025/11/5af1342b987401050004cd7dcdb5c9ae.png)
我对延迟队列的理解：
死信交换机exchange就是普通的的exchange 不同的是 它绑定了一个死信队列 死信队列需要设置x-dead-letter-exchange属性 指定死信交换机
设置x-dead-letter-routing-key属性 指定死信路由键 ttl过期时间可以设置发布者发布的消息上 也可以设置在死信队列上 
消息在过期之后 会进入死信交换机 根据路由键转发到真正的被普通的队列中 被消费者消费
死信队列不能直接被消费者消费 只能通过死信交换机转发到普通队列中 被消费者消费
![](https://cdn.abean.eu.org/2025/11/30c446bfd7525329d4bf1e99a55d7756.png)
创建订单（提交订单）之后就锁定库存了 此时并未支付 
延迟队列的应用场景：
1. 订单超时未支付 取消订单 
2. 订单超时未支付 释放库存
死信队列原理： 消息的TTL + 死信交换机exchange
延迟队列原理： 
1. 消息发送到一个普通队列 设置消息的过期时间TTL
2. 普通队列绑定一个死信交换机 设置死信路由键‘
3. 消息过期后 进入死信交换机 根据路由键转发到真正的延迟队列中 被消费者消费
4. 消费者监听延迟队列 处理业务逻辑
5. 延迟队列的消费者监听真正的业务队列 处理业务逻辑
6. 业务队列消费者处理完业务逻辑后 手动确认ACK
7. 如果消费者处理业务逻辑失败 可以拒绝NACK消息 消息重新入队列 重试消费
8. 如果重试多次仍然失败 可以将消息发送到另一个死信交换机 进行后续处理(人工干预等)
9. 通过设置消息的TTL 可以实现不同的延迟时间 适应不同的业务场景
10. 延迟队列的消费者需要幂等性处理 避免重复消费导致数据不一致
11. 可以结合数据库事务 使用可靠消息投递机制 保证消息不丢失
12. 可以使用RabbitMQ的插件 实现更复杂的延迟队列功能
13. 监控延迟队列的消息积压情况 及时处理异常情况
14. 定期清理过期的消息 避免队列过大影响性能
15. 根据业务需求 调整延迟队列的并发消费数量 提高处理效率
16. 结合分布式锁 保证多实例环境下的消息处理顺序
17. 使用消息追踪功能 方便排查问题
18. 定期评估延迟队列的性能指标 优化配置参数
19. 结合其他中间件 实现更复杂的业务流程
20. 定期备份延迟队列的数据 防止数据丢失