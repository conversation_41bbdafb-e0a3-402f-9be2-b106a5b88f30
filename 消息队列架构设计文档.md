# 电商系统消息队列架构设计文档

## 📋 概述

本文档详细描述了电商系统中消息队列的应用场景架构，重点涵盖订单超时取消、库存释放、秒杀等核心业务场景。

## 🏗️ 整体架构

### 架构层次
1. **用户层**: Web应用、移动应用
2. **网关层**: API网关，统一入口
3. **服务层**: 微服务集群（订单、库存、支付、秒杀等）
4. **消息层**: RabbitMQ消息队列集群
5. **存储层**: 数据库集群、Redis缓存

### 核心组件
- **RabbitMQ集群**: 消息队列中间件
- **延时队列**: 处理订单超时场景
- **Redis**: 缓存和秒杀库存管理
- **微服务**: 订单、库存、支付、秒杀服务

## 🔄 核心业务场景

### 1. 订单超时取消场景

#### 业务流程
1. **订单创建**: 用户提交订单，系统创建待支付订单
2. **库存锁定**: 锁定商品库存，防止超卖
3. **延时消息**: 发送30分钟延时消息到延时队列
4. **超时检查**: 延时消息到期后检查订单支付状态
5. **自动取消**: 未支付订单自动取消，释放库存

#### 技术实现
```yaml
# 延时队列配置
delay.exchange: order.delay.exchange
delay.queue: order.delay.queue
delay.ttl: 1800000  # 30分钟
```

#### 关键特性
- **自动化**: 无需人工干预，系统自动处理
- **准确性**: 精确的时间控制，避免资源浪费
- **可靠性**: 消息持久化，确保不丢失

### 2. 库存管理场景

#### 库存锁定流程
1. **预检查**: 检查商品库存是否充足
2. **锁定操作**: 在数据库中锁定指定数量库存
3. **缓存更新**: 同步更新Redis缓存中的可用库存
4. **消息确认**: 发送库存锁定确认消息

#### 库存释放流程
1. **触发条件**: 订单取消或支付超时
2. **释放操作**: 解除数据库中的库存锁定
3. **缓存恢复**: 恢复Redis中的可用库存数量
4. **状态同步**: 确保数据库和缓存的一致性

#### 技术要点
- **分布式锁**: 防止并发操作导致的数据不一致
- **双写一致性**: 数据库和缓存的同步更新
- **补偿机制**: 异常情况下的数据修复

### 3. 秒杀场景

#### 秒杀流程设计
1. **请求接收**: 用户发起秒杀请求
2. **资格验证**: 检查用户秒杀资格（防刷、限购等）
3. **队列缓冲**: 请求进入消息队列，削峰填谷
4. **异步处理**: 后台异步处理秒杀逻辑
5. **库存扣减**: 使用Redis原子操作扣减库存
6. **订单创建**: 库存扣减成功后创建订单

#### 性能优化策略
- **Redis预热**: 提前将秒杀商品信息加载到Redis
- **限流控制**: 接口层面的流量控制
- **队列削峰**: 消息队列缓解瞬时高并发
- **异步处理**: 提高系统响应速度

## 📊 消息队列设计

### 交换机设计
```yaml
exchanges:
  - name: order.exchange
    type: topic
    durable: true
    
  - name: inventory.exchange
    type: topic
    durable: true
    
  - name: seckill.exchange
    type: direct
    durable: true
    
  - name: delay.exchange
    type: x-delayed-message
    durable: true
```

### 队列设计
```yaml
queues:
  - name: order.create.queue
    durable: true
    routing_key: order.create
    
  - name: order.cancel.queue
    durable: true
    routing_key: order.cancel
    
  - name: inventory.lock.queue
    durable: true
    routing_key: inventory.lock
    
  - name: inventory.release.queue
    durable: true
    routing_key: inventory.release
    
  - name: seckill.queue
    durable: true
    routing_key: seckill.process
```

### 消息格式设计
```json
{
  "messageId": "uuid",
  "timestamp": "2024-01-01T10:00:00Z",
  "eventType": "ORDER_CREATED",
  "data": {
    "orderId": "order_123",
    "userId": "user_456",
    "items": [
      {
        "skuId": "sku_789",
        "quantity": 2,
        "price": 99.99
      }
    ]
  },
  "metadata": {
    "source": "order-service",
    "version": "1.0"
  }
}
```

## 🔧 技术实现要点

### 1. 消息可靠性保证
- **生产者确认**: 确保消息成功发送到交换机
- **消息持久化**: 消息和队列持久化存储
- **消费者确认**: 手动ACK确保消息被正确处理
- **死信队列**: 处理失败消息的兜底机制

### 2. 幂等性处理
- **消息去重**: 基于messageId的幂等性控制
- **业务幂等**: 订单状态检查，避免重复处理
- **分布式锁**: 关键操作的并发控制

### 3. 监控与告警
- **消息积压监控**: 队列长度监控
- **处理延迟监控**: 消息处理时间统计
- **错误率监控**: 消息处理失败率
- **系统资源监控**: CPU、内存、磁盘使用率

## 🚀 部署与运维

### 集群部署
- **RabbitMQ集群**: 3节点集群，保证高可用
- **负载均衡**: HAProxy或Nginx负载均衡
- **数据同步**: 镜像队列确保数据一致性

### 性能调优
- **连接池**: 合理配置连接池大小
- **预取数量**: 优化消费者预取消息数量
- **内存管理**: 合理设置内存水位线
- **磁盘管理**: 定期清理过期消息

### 容灾备份
- **数据备份**: 定期备份队列数据
- **跨机房部署**: 多机房部署提高可用性
- **故障切换**: 自动故障检测和切换机制

## 📈 性能指标

### 关键指标
- **消息吞吐量**: 10万条/秒
- **消息延迟**: 平均 < 10ms
- **系统可用性**: 99.99%
- **数据一致性**: 最终一致性

### 容量规划
- **队列数量**: 根据业务场景合理规划
- **消息大小**: 控制在1KB以内
- **存储容量**: 预留足够的磁盘空间
- **网络带宽**: 考虑峰值流量需求
