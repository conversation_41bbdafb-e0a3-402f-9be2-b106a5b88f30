<svg width="590" height="470" viewBox="0 0 590 470" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bg2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E1F5FE;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#B3E5FC;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="590" height="470" fill="url(#bg2)"/>
  
  <!-- 蔬菜图案 -->
  <rect x="120" y="120" width="60" height="80" rx="10" fill="#4CAF50"/>
  <circle cx="150" cy="110" r="8" fill="#66BB6A"/>
  <text x="110" y="220" font-family="Arial" font-size="16" fill="#1B5E20">新鲜青菜</text>
  
  <circle cx="280" cy="150" r="35" fill="#FF5722"/>
  <path d="M270 140 L290 140 L285 130 Z" fill="#4CAF50"/>
  <text x="250" y="220" font-family="Arial" font-size="16" fill="#1B5E20">红萝卜</text>
  
  <ellipse cx="420" cy="160" rx="25" ry="35" fill="#9C27B0"/>
  <rect x="415" y="125" width="10" height="15" fill="#4CAF50"/>
  <text x="390" y="230" font-family="Arial" font-size="16" fill="#1B5E20">紫茄子</text>
  
  <!-- 主标题 -->
  <text x="50" y="350" font-family="Arial" font-size="36" font-weight="bold" fill="#1B5E20">有机蔬菜</text>
  <text x="50" y="390" font-family="Arial" font-size="24" fill="#2E7D32">绿色健康 营养丰富</text>
  
  <!-- 装饰 -->
  <circle cx="500" cy="300" r="3" fill="#4CAF50" opacity="0.6"/>
  <circle cx="520" cy="320" r="2" fill="#66BB6A" opacity="0.6"/>
  <circle cx="540" cy="310" r="4" fill="#81C784" opacity="0.6"/>
</svg>
