<svg width="590" height="470" viewBox="0 0 590 470" xmlns="http://www.w3.org/2000/svg">
  <!-- 背景渐变 -->
  <defs>
    <linearGradient id="bg3" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FFF3E0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFE0B2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <rect width="590" height="470" fill="url(#bg3)"/>
  
  <!-- 肉类图案 -->
  <ellipse cx="150" cy="150" rx="40" ry="25" fill="#D32F2F"/>
  <text x="120" y="200" font-family="Arial" font-size="16" fill="#B71C1C">优质牛肉</text>
  
  <ellipse cx="300" cy="160" rx="35" ry="30" fill="#F57C00"/>
  <text x="270" y="210" font-family="Arial" font-size="16" fill="#E65100">新鲜鸡肉</text>
  
  <ellipse cx="450" cy="150" rx="30" ry="35" fill="#E91E63"/>
  <text x="420" y="200" font-family="Arial" font-size="16" fill="#AD1457">精选猪肉</text>
  
  <!-- 主标题 -->
  <text x="50" y="350" font-family="Arial" font-size="36" font-weight="bold" fill="#BF360C">优质肉类</text>
  <text x="50" y="390" font-family="Arial" font-size="24" fill="#D84315">新鲜直供 品质保证</text>
  
  <!-- 装饰星星 -->
  <polygon points="500,280 505,290 515,290 507,297 510,307 500,300 490,307 493,297 485,290 495,290" fill="#FF9800" opacity="0.7"/>
  <polygon points="530,320 533,326 539,326 534,330 536,336 530,332 524,336 526,330 521,326 527,326" fill="#FFC107" opacity="0.7"/>
</svg>
