<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8"/>
    <title></title>
    <script type="text/javascript" src="http://cart.vmake.eu.org/static/cart/js/jquery-3.1.1.min.js"></script>
    <script type="text/javascript" src="http://cart.vmake.eu.org/static/cart/bootstrap/js/bootstrap.js"></script>
    <script type="text/javascript" src="http://cart.vmake.eu.org/static/cart/js/swiper.min.js"></script>
    <script src="http://cart.vmake.eu.org/static/cart/js/swiper.min.js"></script>
    <link rel="stylesheet" type="text/css" href="http://cart.vmake.eu.org/static/cart//css/swiper.min.css"/>
    <link rel="stylesheet" type="text/css" href="http://cart.vmake.eu.org/static/cart//bootstrap/css/bootstrap.css"/>
    <link rel="stylesheet" type="text/css" href="http://cart.vmake.eu.org/static/cart//css/success.css"/>

</head>

<body>
<!--头部-->
<div class="alert-info">
    <div class="hd_wrap_top">
        <ul class="hd_wrap_left">
            <li class="hd_home"><i class="glyphicon glyphicon-home"></i>
                <a href="http://vmake.eu.org">易生鲜首页</a>
            </li>

        </ul>

        <ul class="hd_wrap_right">
            <li>
                <a th:if="${session.loginUser==null}" href="http://auth.vmake.eu.org/login.html" class="li_2">请登录</a>
                <a th:if="${session.loginUser!=null}" style="width: 100px">[[${session.loginUser.nickname}]]</a>
            </li>
            <li>
                <a th:if="${session.loginUser == null}" href="http://auth.vmake.eu.org/reg.html">免费注册</a>
            </li>
            <li class="spacer"></li>

            <li>
                <a href="http://cart.vmake.eu.org/javascript:;">我的订单</a>
            </li>


        </ul>

    </div>
</div>

<div class="nav-tabs-justified">
    <div class="nav_wrap">

        <div class="nav_top">
            <div class="nav_top_one">
                <a href="http://cart.vmake.eu.org"><img src="http://cart.vmake.eu.org/static/cart/image/logo1.png" style="height: 60px;width:180px;"/></a>
            </div>
            <div class="nav_top_two"><input type="text"/>
                <button>搜索</button>
            </div>


        </div>

    </div>
</div>

<div class="main">

    <div class="success-wrap">
        <div class="w" id="result">
            <div class="m succeed-box">

                <div th:if="${orderSn != null}" class="mc success-cont">
                    <h1>恭喜，秒杀成功，订单号[[${orderSn}]]</h1>
                    <h2>正在准备订单数据，10s以后自动跳转支付 <a style="color: red" th:href="${'http://order.vmake.eu.org/payOrder?orderSn='+orderSn}">去支付</a></h2>

                </div>
                <div th:if="${orderSn == null}">
                    <h1>手气不好，秒杀失败，下次再来</h1>
                </div>
            </div>
        </div>
    </div>

</div>
</body>
<script type="text/javascript" src="http://cart.vmake.eu.org/static/cart/js/success.js"></script>

</html>
