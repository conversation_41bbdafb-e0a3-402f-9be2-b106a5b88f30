/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 50741
 Source Host           : *************:3306
 Source Schema         : gulimall_wms

 Target Server Type    : MySQL
 Target Server Version : 50741
 File Encoding         : 65001

 Date: 27/10/2025 09:32:37
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for undo_log
-- ----------------------------
DROP TABLE IF EXISTS `undo_log`;
CREATE TABLE `undo_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `branch_id` bigint(20) NOT NULL,
  `xid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `context` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `rollback_info` longblob NOT NULL,
  `log_status` int(11) NOT NULL,
  `log_created` datetime(0) NOT NULL,
  `log_modified` datetime(0) NOT NULL,
  `ext` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ux_undo_log`(`xid`, `branch_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of undo_log
-- ----------------------------

-- ----------------------------
-- Table structure for wms_purchase
-- ----------------------------
DROP TABLE IF EXISTS `wms_purchase`;
CREATE TABLE `wms_purchase`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '采购单id',
  `assignee_id` bigint(20) NULL DEFAULT NULL COMMENT '采购人id',
  `assignee_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '采购人名',
  `phone` char(13) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系方式',
  `priority` int(4) NULL DEFAULT NULL COMMENT '优先级',
  `status` int(4) NULL DEFAULT NULL COMMENT '状态',
  `ware_id` bigint(20) NULL DEFAULT NULL COMMENT '仓库id',
  `amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '总金额',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '采购信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wms_purchase
-- ----------------------------
INSERT INTO `wms_purchase` VALUES (1, 2, 'kevin', '13812345678', 1, 2, NULL, NULL, NULL, '2023-11-02 11:20:29');
INSERT INTO `wms_purchase` VALUES (2, 2, 'kevin', '13812345678', NULL, 4, NULL, NULL, '2023-11-01 22:55:44', '2023-11-02 18:08:44');
INSERT INTO `wms_purchase` VALUES (3, 1, 'admin', '13612345678', NULL, 3, NULL, NULL, '2023-11-02 19:37:45', '2023-11-02 19:40:33');
INSERT INTO `wms_purchase` VALUES (4, 2, 'kevin', '13812345678', 1, 3, NULL, NULL, '2024-03-09 21:03:54', '2024-03-09 21:09:52');
INSERT INTO `wms_purchase` VALUES (5, NULL, '', '', 1, 3, NULL, NULL, '2024-03-09 21:21:27', '2024-03-09 21:21:57');

-- ----------------------------
-- Table structure for wms_purchase_detail
-- ----------------------------
DROP TABLE IF EXISTS `wms_purchase_detail`;
CREATE TABLE `wms_purchase_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `purchase_id` bigint(20) NULL DEFAULT NULL COMMENT '采购单id',
  `sku_id` bigint(20) NULL DEFAULT NULL COMMENT '采购商品id',
  `sku_num` int(11) NULL DEFAULT NULL COMMENT '采购数量',
  `sku_price` decimal(18, 4) NULL DEFAULT NULL COMMENT '采购金额',
  `ware_id` bigint(20) NULL DEFAULT NULL COMMENT '仓库id',
  `status` int(11) NULL DEFAULT NULL COMMENT '状态[0新建，1已分配，2正在采购，3已完成，4采购失败]',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wms_purchase_detail
-- ----------------------------
INSERT INTO `wms_purchase_detail` VALUES (1, 2, 1, 10, NULL, 2, 3);
INSERT INTO `wms_purchase_detail` VALUES (2, 2, 3, 2, NULL, 2, 4);
INSERT INTO `wms_purchase_detail` VALUES (7, 3, 3, 15, NULL, 2, 3);
INSERT INTO `wms_purchase_detail` VALUES (8, 3, 2, 10, NULL, 2, 3);
INSERT INTO `wms_purchase_detail` VALUES (9, 3, 4, 5, NULL, 2, 3);
INSERT INTO `wms_purchase_detail` VALUES (12, 4, 4, 3, NULL, 1, 3);
INSERT INTO `wms_purchase_detail` VALUES (13, 5, 4, 2, NULL, 2, 3);

-- ----------------------------
-- Table structure for wms_ware_info
-- ----------------------------
DROP TABLE IF EXISTS `wms_ware_info`;
CREATE TABLE `wms_ware_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '仓库名',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '仓库地址',
  `areacode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域编码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '仓库信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wms_ware_info
-- ----------------------------
INSERT INTO `wms_ware_info` VALUES (1, '1号仓库', '北京xx', '124');
INSERT INTO `wms_ware_info` VALUES (2, '2号仓库', '上海市', '1111');

-- ----------------------------
-- Table structure for wms_ware_order_task
-- ----------------------------
DROP TABLE IF EXISTS `wms_ware_order_task`;
CREATE TABLE `wms_ware_order_task`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint(20) NULL DEFAULT NULL COMMENT 'order_id',
  `order_sn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'order_sn',
  `consignee` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人',
  `consignee_tel` char(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人电话',
  `delivery_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配送地址',
  `order_comment` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单备注',
  `payment_way` tinyint(1) NULL DEFAULT NULL COMMENT '付款方式【 1:在线付款 2:货到付款】',
  `task_status` tinyint(2) NULL DEFAULT NULL COMMENT '任务状态',
  `order_body` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单描述',
  `tracking_no` char(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流单号',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create_time',
  `ware_id` bigint(20) NULL DEFAULT NULL COMMENT '仓库id',
  `task_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作单备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '库存工作单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wms_ware_order_task
-- ----------------------------
INSERT INTO `wms_ware_order_task` VALUES (1, NULL, '202403131201511511767762974225436673', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (2, NULL, '202403131247469951767774533047681026', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (3, NULL, '202403131417279911767797102580805634', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (4, NULL, '202403131455033701767806562321805313', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (5, NULL, '202403131512505191767811038269128706', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (6, NULL, '202403131516256201767811940434558978', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (7, NULL, '202403131522295261767813466800394242', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (8, NULL, '202403131524015001767813852550533121', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (9, NULL, '202403131538499621767817579047141378', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (10, NULL, '202403131544259241767818988173905921', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (11, NULL, '202403131548482031767820088251756546', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (12, NULL, '202403131559068131767822682890145794', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (13, NULL, '202403131603589811767823908306391041', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (14, NULL, '202403131606511731767824630519402498', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (15, NULL, '202403131611207111767825761081401346', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (16, NULL, '202403131614231461767826526269292545', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (24, NULL, '202404132053092521779251500418076674', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (25, NULL, '202404132055461731779252158596648962', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (26, NULL, '202404132105585121779254726928371714', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (27, NULL, '202404140759576771779419308003168257', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (28, NULL, '202404140800148891779419380170362882', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (29, NULL, '202404140904172061779435495961399297', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (30, NULL, '202404140904195401779435505738321922', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (31, NULL, '202404140943120761779445289115979778', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (32, NULL, '202404151102173421779827580065550337', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (33, NULL, '202404151103174611779827832222912514', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (34, NULL, '202404221939553861782494562002931714', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (35, NULL, '202508241418183401959500494645342209', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (36, NULL, '202508241423379741959501835287506946', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `wms_ware_order_task` VALUES (37, NULL, '202508241502237801959511590416527361', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for wms_ware_order_task_detail
-- ----------------------------
DROP TABLE IF EXISTS `wms_ware_order_task_detail`;
CREATE TABLE `wms_ware_order_task_detail`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `sku_id` bigint(20) NULL DEFAULT NULL COMMENT 'sku_id',
  `sku_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'sku_name',
  `sku_num` int(11) NULL DEFAULT NULL COMMENT '购买个数',
  `task_id` bigint(20) NULL DEFAULT NULL COMMENT '工作单id',
  `ware_id` bigint(20) NULL DEFAULT NULL COMMENT '仓库id',
  `lock_status` int(1) NULL DEFAULT NULL COMMENT '1-锁定 2-解锁 3-扣减',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '库存工作单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wms_ware_order_task_detail
-- ----------------------------
INSERT INTO `wms_ware_order_task_detail` VALUES (1, 3, '', 1, 1, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (2, 1, '', 1, 2, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (3, 1, '', 1, 3, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (4, 1, '', 1, 4, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (5, 1, '', 1, 5, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (6, 1, '', 1, 6, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (7, 1, '', 1, 7, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (8, 1, '', 1, 8, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (9, 1, '', 1, 9, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (10, 1, '', 1, 10, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (11, 1, '', 1, 11, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (12, 1, '', 1, 12, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (13, 1, '', 1, 13, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (14, 1, '', 1, 14, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (15, 1, '', 1, 15, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (16, 1, '', 1, 16, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (24, 1, '', 1, 24, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (25, 2, '', 1, 25, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (26, 3, '', 1, 26, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (27, 1, '', 1, 27, 2, 1);
INSERT INTO `wms_ware_order_task_detail` VALUES (28, 1, '', 1, 28, 2, 1);
INSERT INTO `wms_ware_order_task_detail` VALUES (29, 1, '', 1, 29, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (30, 1, '', 1, 30, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (31, 1, '', 1, 31, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (32, 2, '', 1, 32, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (33, 2, '', 1, 33, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (34, 1, '', 2, 33, 2, 2);
INSERT INTO `wms_ware_order_task_detail` VALUES (35, 1, '', 1, 34, 2, 1);
INSERT INTO `wms_ware_order_task_detail` VALUES (36, 1, '', 1, 35, 2, 1);
INSERT INTO `wms_ware_order_task_detail` VALUES (37, 3, '', 1, 36, 2, 1);
INSERT INTO `wms_ware_order_task_detail` VALUES (38, 2, '', 1, 36, 2, 1);
INSERT INTO `wms_ware_order_task_detail` VALUES (39, 3, '', 1, 37, 2, 1);
INSERT INTO `wms_ware_order_task_detail` VALUES (40, 2, '', 1, 37, 2, 1);

-- ----------------------------
-- Table structure for wms_ware_sku
-- ----------------------------
DROP TABLE IF EXISTS `wms_ware_sku`;
CREATE TABLE `wms_ware_sku`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `sku_id` bigint(20) NULL DEFAULT NULL COMMENT 'sku_id',
  `ware_id` bigint(20) NULL DEFAULT NULL COMMENT '仓库id',
  `stock` int(11) NULL DEFAULT NULL COMMENT '库存数',
  `sku_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'sku_name',
  `stock_locked` int(11) NULL DEFAULT NULL COMMENT '锁定库存',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品库存' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of wms_ware_sku
-- ----------------------------
INSERT INTO `wms_ware_sku` VALUES (1, 1, 2, 10, NULL, 4);
INSERT INTO `wms_ware_sku` VALUES (2, 3, 2, 15, NULL, 4);
INSERT INTO `wms_ware_sku` VALUES (3, 2, 2, 10, NULL, 2);
INSERT INTO `wms_ware_sku` VALUES (4, 4, 2, 7, NULL, 0);
INSERT INTO `wms_ware_sku` VALUES (6, 4, 1, 3, '华为 HUAWEI Mate 30 Pro 亮黑色 8G+256G', 0);

SET FOREIGN_KEY_CHECKS = 1;
