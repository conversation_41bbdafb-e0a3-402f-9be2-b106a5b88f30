/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 50741
 Source Host           : *************:3306
 Source Schema         : gulimall_oms

 Target Server Type    : MySQL
 Target Server Version : 50741
 File Encoding         : 65001

 Date: 08/09/2025 17:43:03
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mq_message
-- ----------------------------
DROP TABLE IF EXISTS `mq_message`;
CREATE TABLE `mq_message`  (
  `message_id` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `to_exchane` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `routing_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `class_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `message_status` int(1) NULL DEFAULT 0 COMMENT '0-新建 1-已发送 2-错误抵达 3-已抵达',
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`message_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of mq_message
-- ----------------------------

-- ----------------------------
-- Table structure for oms_order
-- ----------------------------
DROP TABLE IF EXISTS `oms_order`;
CREATE TABLE `oms_order`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `member_id` bigint(20) NULL DEFAULT NULL COMMENT 'member_id',
  `order_sn` char(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单号',
  `coupon_id` bigint(20) NULL DEFAULT NULL COMMENT '使用的优惠券',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create_time',
  `member_username` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `total_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '订单总额',
  `pay_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '应付总额',
  `freight_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '运费金额',
  `promotion_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '促销优化金额（促销价、满减、阶梯价）',
  `integration_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '积分抵扣金额',
  `coupon_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '优惠券抵扣金额',
  `discount_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '后台调整订单使用的折扣金额',
  `pay_type` tinyint(4) NULL DEFAULT NULL COMMENT '支付方式【1->支付宝；2->微信；3->银联； 4->货到付款；】',
  `source_type` tinyint(4) NULL DEFAULT NULL COMMENT '订单来源[0->PC订单；1->app订单]',
  `status` tinyint(4) NULL DEFAULT NULL COMMENT '订单状态【0->待付款；1->待发货；2->已发货；3->已完成；4->已关闭；5->无效订单】',
  `delivery_company` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流公司(配送方式)',
  `delivery_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '物流单号',
  `auto_confirm_day` int(11) NULL DEFAULT NULL COMMENT '自动确认时间（天）',
  `integration` int(11) NULL DEFAULT NULL COMMENT '可以获得的积分',
  `growth` int(11) NULL DEFAULT NULL COMMENT '可以获得的成长值',
  `bill_type` tinyint(4) NULL DEFAULT NULL COMMENT '发票类型[0->不开发票；1->电子发票；2->纸质发票]',
  `bill_header` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发票抬头',
  `bill_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发票内容',
  `bill_receiver_phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收票人电话',
  `bill_receiver_email` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收票人邮箱',
  `receiver_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人姓名',
  `receiver_phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人电话',
  `receiver_post_code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人邮编',
  `receiver_province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省份/直辖市',
  `receiver_city` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市',
  `receiver_region` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区',
  `receiver_detail_address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单备注',
  `confirm_status` tinyint(4) NULL DEFAULT NULL COMMENT '确认收货状态[0->未确认；1->已确认]',
  `delete_status` tinyint(4) NULL DEFAULT NULL COMMENT '删除状态【0->未删除；1->已删除】',
  `use_integration` int(11) NULL DEFAULT NULL COMMENT '下单时使用的积分',
  `payment_time` datetime(0) NULL DEFAULT NULL COMMENT '支付时间',
  `delivery_time` datetime(0) NULL DEFAULT NULL COMMENT '发货时间',
  `receive_time` datetime(0) NULL DEFAULT NULL COMMENT '确认收货时间',
  `comment_time` datetime(0) NULL DEFAULT NULL COMMENT '评价时间',
  `modify_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_sn`(`order_sn`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 46 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of oms_order
-- ----------------------------
INSERT INTO `oms_order` VALUES (6, 3, '202403131201511511767762974225436673', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 12:01:51');
INSERT INTO `oms_order` VALUES (7, 3, '202403131247469951767774533047681026', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 12:47:47');
INSERT INTO `oms_order` VALUES (8, 3, '202403131417279911767797102580805634', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 14:17:28');
INSERT INTO `oms_order` VALUES (9, 3, '202403131455033701767806562321805313', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 14:55:04');
INSERT INTO `oms_order` VALUES (10, 3, '202403131512505191767811038269128706', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 15:12:51');
INSERT INTO `oms_order` VALUES (11, 3, '202403131516256201767811940434558978', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 15:16:26');
INSERT INTO `oms_order` VALUES (12, 3, '202403131522295261767813466800394242', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 15:22:30');
INSERT INTO `oms_order` VALUES (13, 3, '202403131524015001767813852550533121', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 15:24:02');
INSERT INTO `oms_order` VALUES (14, 3, '202403131538499621767817579047141378', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 15:38:50');
INSERT INTO `oms_order` VALUES (15, 3, '202403131544259241767818988173905921', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 15:44:26');
INSERT INTO `oms_order` VALUES (16, 3, '202403131548482031767820088251756546', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 15:48:48');
INSERT INTO `oms_order` VALUES (17, 3, '202403131559068131767822682890145794', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 15:59:07');
INSERT INTO `oms_order` VALUES (18, 3, '202403131603589811767823908306391041', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 16:03:59');
INSERT INTO `oms_order` VALUES (19, 3, '202403131606511731767824630519402498', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 16:06:51');
INSERT INTO `oms_order` VALUES (20, 3, '202403131611207111767825761081401346', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 16:11:21');
INSERT INTO `oms_order` VALUES (21, 3, '202403131614231461767826526269292545', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-03-13 16:14:23');
INSERT INTO `oms_order` VALUES (22, 3, '202403210342032421770536298718412801', NULL, '2024-03-21 03:42:04', NULL, NULL, 800.0000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `oms_order` VALUES (31, 3, '202404132053092521779251500418076674', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-04-14 04:53:09');
INSERT INTO `oms_order` VALUES (32, 3, '202404132055461731779252158596648962', NULL, NULL, NULL, 6299.0000, 6307.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 6299, 6299, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-04-14 04:55:46');
INSERT INTO `oms_order` VALUES (33, 3, '202404132105585121779254726928371714', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-04-14 05:05:59');
INSERT INTO `oms_order` VALUES (34, 3, '202404140759576771779419308003168257', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 0, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-04-14 16:00:00');
INSERT INTO `oms_order` VALUES (35, 3, '202404140904172061779435495961399297', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-04-14 17:04:17');
INSERT INTO `oms_order` VALUES (36, 3, '202404140904195401779435505738321922', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-04-14 17:04:20');
INSERT INTO `oms_order` VALUES (37, 3, '202404140913433271779437870482722818', NULL, '2024-04-14 17:13:44', NULL, NULL, 1.0000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `oms_order` VALUES (38, 3, '202404140917147481779438757204725761', NULL, '2024-04-14 17:17:15', NULL, NULL, 800.0000, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `oms_order` VALUES (39, 3, '202404140943120761779445289115979778', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-04-14 17:43:12');
INSERT INTO `oms_order` VALUES (40, 3, '202404151102173421779827580065550337', NULL, NULL, NULL, 6299.0000, 6307.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 6299, 6299, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-04-15 19:02:17');
INSERT INTO `oms_order` VALUES (41, 3, '202404151103174611779827832222912514', NULL, NULL, NULL, 17897.0000, 17905.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 4, NULL, NULL, 7, 17897, 17897, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-04-15 19:03:18');
INSERT INTO `oms_order` VALUES (42, 3, '202404221939553861782494562002931714', NULL, NULL, NULL, 5799.0000, 5807.0000, 8.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 0, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, 'kevin', '18812345678', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2024-04-23 03:39:57');
INSERT INTO `oms_order` VALUES (43, 4, '202508241418183401959500494645342209', NULL, NULL, NULL, 5799.0000, 5806.0000, 7.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 0, NULL, NULL, 7, 5799, 5799, NULL, NULL, NULL, NULL, NULL, '11之焕', '18812345677', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2025-08-24 14:18:19');
INSERT INTO `oms_order` VALUES (44, 4, '202508241423379741959501835287506946', NULL, NULL, NULL, 12098.0000, 12105.0000, 7.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 0, NULL, NULL, 7, 12098, 12098, NULL, NULL, NULL, NULL, NULL, '11之焕', '18812345677', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2025-08-24 14:23:38');
INSERT INTO `oms_order` VALUES (45, 4, '202508241502237801959511590416527361', NULL, NULL, NULL, 12098.0000, 12105.0000, 7.0000, 0.0000, 0.0000, 0.0000, NULL, NULL, NULL, 0, NULL, NULL, 7, 12098, 12098, NULL, NULL, NULL, NULL, NULL, '11之焕', '18812345677', NULL, '上海市', NULL, NULL, '上海市松江区大江大厦6层', NULL, NULL, 0, NULL, NULL, NULL, NULL, NULL, '2025-08-24 15:02:24');

-- ----------------------------
-- Table structure for oms_order_item
-- ----------------------------
DROP TABLE IF EXISTS `oms_order_item`;
CREATE TABLE `oms_order_item`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint(20) NULL DEFAULT NULL COMMENT 'order_id',
  `order_sn` char(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'order_sn',
  `spu_id` bigint(20) NULL DEFAULT NULL COMMENT 'spu_id',
  `spu_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'spu_name',
  `spu_pic` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'spu_pic',
  `spu_brand` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '品牌',
  `category_id` bigint(20) NULL DEFAULT NULL COMMENT '商品分类id',
  `sku_id` bigint(20) NULL DEFAULT NULL COMMENT '商品sku编号',
  `sku_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品sku名字',
  `sku_pic` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品sku图片',
  `sku_price` decimal(18, 4) NULL DEFAULT NULL COMMENT '商品sku价格',
  `sku_quantity` int(11) NULL DEFAULT NULL COMMENT '商品购买的数量',
  `sku_attrs_vals` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品销售属性组合（JSON）',
  `promotion_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '商品促销分解金额',
  `coupon_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '优惠券优惠分解金额',
  `integration_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '积分优惠分解金额',
  `real_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '该商品经过优惠后的分解金额',
  `gift_integration` int(11) NULL DEFAULT NULL COMMENT '赠送积分',
  `gift_growth` int(11) NULL DEFAULT NULL COMMENT '赠送成长值',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 48 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单项信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of oms_order_item
-- ----------------------------
INSERT INTO `oms_order_item` VALUES (5, NULL, '202403131201511511767762974225436673', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 3, '华为 HUAWEI Mate 30 Pro 亮黑色 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/b9ba6ac1-fd96-451d-8e0b-53ce4e4bf29b_8bf441260bffa42f.jpg', 5799.0000, 1, '颜色：亮黑色;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (6, NULL, '202403131247469951767774533047681026', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (7, NULL, '202403131417279911767797102580805634', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (8, NULL, '202403131455033701767806562321805313', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (9, NULL, '202403131512505191767811038269128706', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (10, NULL, '202403131516256201767811940434558978', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (11, NULL, '202403131522295261767813466800394242', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (12, NULL, '202403131524015001767813852550533121', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (13, NULL, '202403131538499621767817579047141378', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (14, NULL, '202403131544259241767818988173905921', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (15, NULL, '202403131548482031767820088251756546', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (16, NULL, '202403131559068131767822682890145794', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (17, NULL, '202403131603589811767823908306391041', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (18, NULL, '202403131606511731767824630519402498', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (19, NULL, '202403131611207111767825761081401346', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (20, NULL, '202403131614231461767826526269292545', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (21, NULL, '202403210342032421770536298718412801', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 2, NULL, NULL, 800.0000, 1, NULL, NULL, NULL, NULL, 800.0000, NULL, NULL);
INSERT INTO `oms_order_item` VALUES (30, NULL, '202404132053092521779251500418076674', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (31, NULL, '202404132055461731779252158596648962', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 2, '华为 HUAWEI Mate 30 Pro 星河银 8G+256G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 6299.0000, 1, '颜色：星河银;版本：8G+256G', 0.0000, 0.0000, 0.0000, 6299.0000, 6299, 6299);
INSERT INTO `oms_order_item` VALUES (32, NULL, '202404132105585121779254726928371714', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 3, '华为 HUAWEI Mate 30 Pro 亮黑色 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/b9ba6ac1-fd96-451d-8e0b-53ce4e4bf29b_8bf441260bffa42f.jpg', 5799.0000, 1, '颜色：亮黑色;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (33, NULL, '202404140759576771779419308003168257', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (34, NULL, '202404140904172061779435495961399297', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (35, NULL, '202404140904195401779435505738321922', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (36, NULL, '202404140913433271779437870482722818', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, NULL, NULL, 1.0000, 1, NULL, NULL, NULL, NULL, 1.0000, NULL, NULL);
INSERT INTO `oms_order_item` VALUES (37, NULL, '202404140917147481779438757204725761', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 2, NULL, NULL, 800.0000, 1, NULL, NULL, NULL, NULL, 800.0000, NULL, NULL);
INSERT INTO `oms_order_item` VALUES (38, NULL, '202404140943120761779445289115979778', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (39, NULL, '202404151102173421779827580065550337', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 2, '华为 HUAWEI Mate 30 Pro 星河银 8G+256G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 6299.0000, 1, '颜色：星河银;版本：8G+256G', 0.0000, 0.0000, 0.0000, 6299.0000, 6299, 6299);
INSERT INTO `oms_order_item` VALUES (40, NULL, '202404151103174611779827832222912514', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 2, '华为 HUAWEI Mate 30 Pro 星河银 8G+256G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 6299.0000, 1, '颜色：星河银;版本：8G+256G', 0.0000, 0.0000, 0.0000, 6299.0000, 6299, 6299);
INSERT INTO `oms_order_item` VALUES (41, NULL, '202404151103174611779827832222912514', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 2, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 11598.0000, 11598, 11598);
INSERT INTO `oms_order_item` VALUES (42, NULL, '202404221939553861782494562002931714', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (43, NULL, '202508241418183401959500494645342209', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 1, '华为 HUAWEI Mate 30 Pro 星河银 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 5799.0000, 1, '颜色：星河银;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (44, NULL, '202508241423379741959501835287506946', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 3, '华为 HUAWEI Mate 30 Pro 亮黑色 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/b9ba6ac1-fd96-451d-8e0b-53ce4e4bf29b_8bf441260bffa42f.jpg', 5799.0000, 1, '颜色：亮黑色;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (45, NULL, '202508241423379741959501835287506946', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 2, '华为 HUAWEI Mate 30 Pro 星河银 8G+256G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 6299.0000, 1, '颜色：星河银;版本：8G+256G', 0.0000, 0.0000, 0.0000, 6299.0000, 6299, 6299);
INSERT INTO `oms_order_item` VALUES (46, NULL, '202508241502237801959511590416527361', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 3, '华为 HUAWEI Mate 30 Pro 亮黑色 8G+128G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/b9ba6ac1-fd96-451d-8e0b-53ce4e4bf29b_8bf441260bffa42f.jpg', 5799.0000, 1, '颜色：亮黑色;版本：8G+128G', 0.0000, 0.0000, 0.0000, 5799.0000, 5799, 5799);
INSERT INTO `oms_order_item` VALUES (47, NULL, '202508241502237801959511590416527361', 2, '华为 HUAWEI Mate 30 Pro', NULL, '1', 225, 2, '华为 HUAWEI Mate 30 Pro 星河银 8G+256G 麒麟990 OLED环幕屏双4000万徕卡电影四摄 4G全网通游戏手机', 'https://guanzhuo-vod-project.oss-cn-guangzhou.aliyuncs.com/2023-10-30/75ef5f24-ecb8-4e6e-87f8-8028ef3ec649_919c850652e98031.jpg', 6299.0000, 1, '颜色：星河银;版本：8G+256G', 0.0000, 0.0000, 0.0000, 6299.0000, 6299, 6299);

-- ----------------------------
-- Table structure for oms_order_operate_history
-- ----------------------------
DROP TABLE IF EXISTS `oms_order_operate_history`;
CREATE TABLE `oms_order_operate_history`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint(20) NULL DEFAULT NULL COMMENT '订单id',
  `operate_man` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作人[用户；系统；后台管理员]',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '操作时间',
  `order_status` tinyint(4) NULL DEFAULT NULL COMMENT '订单状态【0->待付款；1->待发货；2->已发货；3->已完成；4->已关闭；5->无效订单】',
  `note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单操作历史记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of oms_order_operate_history
-- ----------------------------

-- ----------------------------
-- Table structure for oms_order_return_apply
-- ----------------------------
DROP TABLE IF EXISTS `oms_order_return_apply`;
CREATE TABLE `oms_order_return_apply`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint(20) NULL DEFAULT NULL COMMENT 'order_id',
  `sku_id` bigint(20) NULL DEFAULT NULL COMMENT '退货商品id',
  `order_sn` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单编号',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '申请时间',
  `member_username` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会员用户名',
  `return_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '退款金额',
  `return_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退货人姓名',
  `return_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退货人电话',
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '申请状态[0->待处理；1->退货中；2->已完成；3->已拒绝]',
  `handle_time` datetime(0) NULL DEFAULT NULL COMMENT '处理时间',
  `sku_img` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品图片',
  `sku_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品名称',
  `sku_brand` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品品牌',
  `sku_attrs_vals` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品销售属性(JSON)',
  `sku_count` int(11) NULL DEFAULT NULL COMMENT '退货数量',
  `sku_price` decimal(18, 4) NULL DEFAULT NULL COMMENT '商品单价',
  `sku_real_price` decimal(18, 4) NULL DEFAULT NULL COMMENT '商品实际支付单价',
  `reason` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原因',
  `description述` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `desc_pics` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '凭证图片，以逗号隔开',
  `handle_note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理备注',
  `handle_man` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '处理人员',
  `receive_man` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货人',
  `receive_time` datetime(0) NULL DEFAULT NULL COMMENT '收货时间',
  `receive_note` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货备注',
  `receive_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '收货电话',
  `company_address` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '公司收货地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单退货申请' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of oms_order_return_apply
-- ----------------------------

-- ----------------------------
-- Table structure for oms_order_return_reason
-- ----------------------------
DROP TABLE IF EXISTS `oms_order_return_reason`;
CREATE TABLE `oms_order_return_reason`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退货原因名',
  `sort` int(11) NULL DEFAULT NULL COMMENT '排序',
  `status` tinyint(1) NULL DEFAULT NULL COMMENT '启用状态',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT 'create_time',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退货原因' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of oms_order_return_reason
-- ----------------------------

-- ----------------------------
-- Table structure for oms_order_setting
-- ----------------------------
DROP TABLE IF EXISTS `oms_order_setting`;
CREATE TABLE `oms_order_setting`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `flash_order_overtime` int(11) NULL DEFAULT NULL COMMENT '秒杀订单超时关闭时间(分)',
  `normal_order_overtime` int(11) NULL DEFAULT NULL COMMENT '正常订单超时时间(分)',
  `confirm_overtime` int(11) NULL DEFAULT NULL COMMENT '发货后自动确认收货时间（天）',
  `finish_overtime` int(11) NULL DEFAULT NULL COMMENT '自动完成交易时间，不能申请退货（天）',
  `comment_overtime` int(11) NULL DEFAULT NULL COMMENT '订单完成后自动好评时间（天）',
  `member_level` tinyint(2) NULL DEFAULT NULL COMMENT '会员等级【0-不限会员等级，全部通用；其他-对应的其他会员等级】',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单配置信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of oms_order_setting
-- ----------------------------

-- ----------------------------
-- Table structure for oms_payment_info
-- ----------------------------
DROP TABLE IF EXISTS `oms_payment_info`;
CREATE TABLE `oms_payment_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_sn` char(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单号（对外业务号）',
  `order_id` bigint(20) NULL DEFAULT NULL COMMENT '订单id',
  `alipay_trade_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付宝交易流水号',
  `total_amount` decimal(18, 4) NULL DEFAULT NULL COMMENT '支付总金额',
  `subject` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易内容',
  `payment_status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付状态',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `confirm_time` datetime(0) NULL DEFAULT NULL COMMENT '确认时间',
  `callback_content` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回调内容',
  `callback_time` datetime(0) NULL DEFAULT NULL COMMENT '回调时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_sn`(`order_sn`) USING BTREE,
  UNIQUE INDEX `alipay_trade_no`(`alipay_trade_no`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '支付信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of oms_payment_info
-- ----------------------------

-- ----------------------------
-- Table structure for oms_refund_info
-- ----------------------------
DROP TABLE IF EXISTS `oms_refund_info`;
CREATE TABLE `oms_refund_info`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_return_id` bigint(20) NULL DEFAULT NULL COMMENT '退款的订单',
  `refund` decimal(18, 4) NULL DEFAULT NULL COMMENT '退款金额',
  `refund_sn` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款交易流水号',
  `refund_status` tinyint(1) NULL DEFAULT NULL COMMENT '退款状态',
  `refund_channel` tinyint(4) NULL DEFAULT NULL COMMENT '退款渠道[1-支付宝，2-微信，3-银联，4-汇款]',
  `refund_content` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退款信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of oms_refund_info
-- ----------------------------

-- ----------------------------
-- Table structure for undo_log
-- ----------------------------
DROP TABLE IF EXISTS `undo_log`;
CREATE TABLE `undo_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `branch_id` bigint(20) NOT NULL,
  `xid` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `context` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `rollback_info` longblob NOT NULL,
  `log_status` int(11) NOT NULL,
  `log_created` datetime(0) NOT NULL,
  `log_modified` datetime(0) NOT NULL,
  `ext` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ux_undo_log`(`xid`, `branch_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of undo_log
-- ----------------------------

SET FOREIGN_KEY_CHECKS = 1;
