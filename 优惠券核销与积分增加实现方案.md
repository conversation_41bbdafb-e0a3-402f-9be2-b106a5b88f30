# 优惠券核销与积分增加实现方案

## 📋 问题分析

根据项目代码分析，当前订单流程中缺少优惠券核销和积分增加功能。需要确定最佳的实现时机和方案。

## 🎯 推荐方案：支付成功后处理

### ⭐ 为什么选择支付成功后处理？

1. **业务安全性**
   - 只有确认支付成功，交易才真正完成
   - 避免订单取消导致的优惠券和积分回滚问题
   - 符合电商业务的一般规律

2. **数据一致性**
   - 减少补偿事务的复杂度
   - 避免分布式事务的性能问题
   - 降低系统复杂性

3. **用户体验**
   - 用户支付成功后立即获得积分奖励
   - 优惠券使用状态及时更新
   - 避免用户困惑（订单取消但优惠券已使用）

## 🏗️ 实现架构

### 1. 订单创建阶段
```java
// OrderServiceImpl.java - submitOrder方法中
@Override
public SubmitOrderResponseVo submitOrder(OrderSubmitVo vo) {
    // ... 现有逻辑 ...
    
    // 验证优惠券但不核销
    if (vo.getCouponId() != null) {
        R couponValidation = couponFeignService.validateCoupon(vo.getCouponId(), memberRespVo.getId());
        if (couponValidation.getCode() != 0) {
            response.setCode(4); // 优惠券无效
            return response;
        }
        // 在订单中记录优惠券ID，但不核销
        orderEntity.setCouponId(vo.getCouponId());
    }
    
    // ... 其他逻辑 ...
}
```

### 2. 支付成功回调处理
```java
// OrderServiceImpl.java - handlePayResult方法修改
@Override
public String handlePayResult(PayAsyncVo vo) {
    // 保存交易流水
    PaymentInfoEntity paymentInfoEntity = new PaymentInfoEntity();
    // ... 现有逻辑 ...
    
    // 修改订单状态
    if (vo.getTrade_status().equals("TRADE_SUCCESS") || vo.getTrade_status().equals("TRADE_FINISHED")) {
        String outTradeNo = vo.getOut_trade_no();
        baseMapper.updateOrderStatus(outTradeNo, OrderStatusEnum.PAYED.getCode());
        
        // 🆕 发送支付成功消息到MQ，异步处理优惠券核销和积分增加
        OrderEntity orderEntity = getOrderByOrderSn(outTradeNo);
        PaymentSuccessTo paymentSuccessTo = new PaymentSuccessTo();
        paymentSuccessTo.setOrderSn(outTradeNo);
        paymentSuccessTo.setOrderId(orderEntity.getId());
        paymentSuccessTo.setMemberId(orderEntity.getMemberId());
        paymentSuccessTo.setCouponId(orderEntity.getCouponId());
        paymentSuccessTo.setPayAmount(orderEntity.getPayAmount());
        
        rabbitTemplate.convertAndSend("order-event-exchange", "order.payment.success", paymentSuccessTo);
    }
    return "success";
}
```

### 3. 消息队列配置
```yaml
# 新增支付成功队列配置
rabbitmq:
  queues:
    payment-success:
      name: mall.order.payment.success.queue
      durable: true
      auto-delete: false
      routing-key: order.payment.success
      exchange: order-event-exchange
```

### 4. 支付成功消息监听器
```java
@Component
@RabbitListener(queues = "mall.order.payment.success.queue")
public class PaymentSuccessListener {
    
    @Autowired
    private CouponFeignService couponFeignService;
    
    @Autowired
    private MemberFeignService memberFeignService;
    
    @RabbitHandler
    public void handlePaymentSuccess(PaymentSuccessTo paymentSuccessTo, Channel channel, Message message) {
        try {
            // 1. 核销优惠券
            if (paymentSuccessTo.getCouponId() != null) {
                R couponResult = couponFeignService.useCoupon(paymentSuccessTo.getCouponId(), 
                                                            paymentSuccessTo.getMemberId(), 
                                                            paymentSuccessTo.getOrderId());
                if (couponResult.getCode() != 0) {
                    log.error("优惠券核销失败: {}", couponResult.getMsg());
                    // 发送到死信队列或重试
                    throw new RuntimeException("优惠券核销失败");
                }
            }
            
            // 2. 增加积分
            Integer points = calculatePoints(paymentSuccessTo.getPayAmount());
            if (points > 0) {
                R pointsResult = memberFeignService.addPoints(paymentSuccessTo.getMemberId(), 
                                                            points, 
                                                            "订单支付获得积分");
                if (pointsResult.getCode() != 0) {
                    log.error("积分增加失败: {}", pointsResult.getMsg());
                    throw new RuntimeException("积分增加失败");
                }
            }
            
            // 3. 发送通知消息（可选）
            sendNotification(paymentSuccessTo);
            
            // 手动确认消息
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            
        } catch (Exception e) {
            log.error("支付成功后处理失败", e);
            try {
                // 拒绝消息，重新入队
                channel.basicReject(message.getMessageProperties().getDeliveryTag(), true);
            } catch (IOException ioException) {
                log.error("消息拒绝失败", ioException);
            }
        }
    }
    
    private Integer calculatePoints(BigDecimal payAmount) {
        // 积分计算规则：每消费1元获得1积分
        return payAmount.intValue();
    }
}
```

## 🔧 需要新增的服务接口

### 1. 优惠券服务接口
```java
// CouponFeignService.java 新增方法
@FeignClient("mall-coupon")
public interface CouponFeignService {
    
    // 验证优惠券有效性
    @PostMapping("/coupon/coupon/validate")
    R validateCoupon(@RequestParam("couponId") Long couponId, 
                    @RequestParam("memberId") Long memberId);
    
    // 核销优惠券
    @PostMapping("/coupon/coupon/use")
    R useCoupon(@RequestParam("couponId") Long couponId, 
               @RequestParam("memberId") Long memberId,
               @RequestParam("orderId") Long orderId);
}
```

### 2. 会员服务接口
```java
// MemberFeignService.java 新增方法
@FeignClient("mall-member")
public interface MemberFeignService {
    
    // 增加积分
    @PostMapping("/member/member/addPoints")
    R addPoints(@RequestParam("memberId") Long memberId,
               @RequestParam("points") Integer points,
               @RequestParam("note") String note);
}
```

## 📊 数据库表结构调整

### 1. 订单表新增字段
```sql
-- oms_order 表新增优惠券字段
ALTER TABLE oms_order ADD COLUMN coupon_id BIGINT COMMENT '使用的优惠券ID';
ALTER TABLE oms_order ADD COLUMN coupon_amount DECIMAL(18,4) COMMENT '优惠券抵扣金额';
```

### 2. 优惠券历史表
```sql
-- sms_coupon_history 表已存在，需要确保字段完整
-- 使用状态[0->未使用；1->已使用；2->已过期]
-- 订单ID和订单号字段用于关联
```

### 3. 积分变化历史表
```sql
-- ums_integration_change_history 表已存在
-- 来源[0->购物；1->管理员修改;2->活动]
-- 购物获得积分使用来源类型 0
```

## 🚨 异常处理机制

### 1. 重试机制
```yaml
# 消息队列重试配置
spring:
  rabbitmq:
    listener:
      simple:
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          multiplier: 2
```

### 2. 死信队列
```java
// 配置死信队列处理失败消息
@Bean
public Queue paymentSuccessDeadLetterQueue() {
    return QueueBuilder.durable("mall.order.payment.success.dlq").build();
}
```

### 3. 补偿机制
```java
// 定时任务检查未处理的支付成功订单
@Scheduled(fixedRate = 300000) // 5分钟执行一次
public void compensatePaymentSuccess() {
    // 查询支付成功但未处理优惠券核销和积分增加的订单
    // 重新发送消息到队列处理
}
```

## 📈 性能优化建议

### 1. 异步处理
- 使用消息队列异步处理，不影响支付回调响应速度
- 批量处理积分更新，提高数据库性能

### 2. 缓存优化
- 优惠券信息缓存到Redis，减少数据库查询
- 用户积分信息缓存，提高查询效率

### 3. 监控告警
- 监控消息队列积压情况
- 监控优惠券核销和积分增加的成功率
- 设置异常告警机制

## 🎯 实施步骤

1. **第一阶段**: 完善订单表结构，新增优惠券相关字段
2. **第二阶段**: 实现优惠券和会员服务的相关接口
3. **第三阶段**: 修改订单创建和支付回调逻辑
4. **第四阶段**: 实现消息队列监听器和异常处理
5. **第五阶段**: 添加监控和补偿机制
6. **第六阶段**: 全面测试和上线

## ✅ 总结

**推荐在支付成功回调之后处理优惠券核销和积分增加**，这样可以：

1. 确保交易的真实性和完整性
2. 避免复杂的补偿事务
3. 提供更好的用户体验
4. 降低系统复杂度
5. 提高数据一致性

通过消息队列异步处理，既保证了业务的可靠性，又不影响支付回调的响应性能。
