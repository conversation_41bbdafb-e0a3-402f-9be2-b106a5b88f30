# RabbitMQ 消息队列配置示例
# 适用于电商系统的订单、库存、秒杀等场景

spring:
  rabbitmq:
    host: localhost
    port: 5672
    username: admin
    password: admin123
    virtual-host: /mall
    # 连接池配置
    connection-timeout: 15000
    # 开启发送确认
    publisher-confirm-type: correlated
    publisher-returns: true
    # 消费者配置
    listener:
      simple:
        # 手动确认消息
        acknowledge-mode: manual
        # 并发消费者数量
        concurrency: 5
        max-concurrency: 10
        # 预取数量
        prefetch: 1
        # 重试配置
        retry:
          enabled: true
          initial-interval: 1000
          max-attempts: 3
          multiplier: 2

---
# 交换机和队列配置
rabbitmq:
  exchanges:
    # 订单交换机
    order:
      name: mall.order.exchange
      type: topic
      durable: true
      auto-delete: false
    
    # 库存交换机
    inventory:
      name: mall.inventory.exchange
      type: topic
      durable: true
      auto-delete: false
    
    # 秒杀交换机
    seckill:
      name: mall.seckill.exchange
      type: direct
      durable: true
      auto-delete: false
    
    # 延时交换机（需要安装延时插件）
    delay:
      name: mall.delay.exchange
      type: x-delayed-message
      durable: true
      auto-delete: false
      arguments:
        x-delayed-type: direct

  queues:
    # 订单相关队列
    order-create:
      name: mall.order.create.queue
      durable: true
      auto-delete: false
      routing-key: order.create
      exchange: mall.order.exchange
      
    order-cancel:
      name: mall.order.cancel.queue
      durable: true
      auto-delete: false
      routing-key: order.cancel
      exchange: mall.order.exchange
      
    order-pay-success:
      name: mall.order.pay.success.queue
      durable: true
      auto-delete: false
      routing-key: order.pay.success
      exchange: mall.order.exchange
    
    # 库存相关队列
    inventory-lock:
      name: mall.inventory.lock.queue
      durable: true
      auto-delete: false
      routing-key: inventory.lock
      exchange: mall.inventory.exchange
      
    inventory-release:
      name: mall.inventory.release.queue
      durable: true
      auto-delete: false
      routing-key: inventory.release
      exchange: mall.inventory.exchange
    
    # 秒杀队列
    seckill-order:
      name: mall.seckill.order.queue
      durable: true
      auto-delete: false
      routing-key: seckill.order
      exchange: mall.seckill.exchange
      # 限制队列长度，防止内存溢出
      arguments:
        x-max-length: 10000
        x-overflow: reject-publish
    
    # 延时队列（订单超时取消）
    order-delay:
      name: mall.order.delay.queue
      durable: true
      auto-delete: false
      routing-key: order.delay
      exchange: mall.delay.exchange
      # 30分钟超时
      arguments:
        x-message-ttl: 1800000
        x-dead-letter-exchange: mall.order.exchange
        x-dead-letter-routing-key: order.cancel

  # 死信队列配置
  dead-letter:
    exchange: mall.dead.letter.exchange
    queue: mall.dead.letter.queue
    routing-key: dead.letter

---
# 消息模板配置
message:
  templates:
    # 订单创建消息
    order-created:
      exchange: mall.order.exchange
      routing-key: order.create
      mandatory: true
      
    # 库存锁定消息
    inventory-lock:
      exchange: mall.inventory.exchange
      routing-key: inventory.lock
      mandatory: true
      
    # 库存释放消息
    inventory-release:
      exchange: mall.inventory.exchange
      routing-key: inventory.release
      mandatory: true
      
    # 订单延时取消消息
    order-delay-cancel:
      exchange: mall.delay.exchange
      routing-key: order.delay
      mandatory: true
      # 延时30分钟
      delay: 1800000
      
    # 秒杀订单消息
    seckill-order:
      exchange: mall.seckill.exchange
      routing-key: seckill.order
      mandatory: true

---
# 业务配置
business:
  order:
    # 订单超时时间（分钟）
    timeout-minutes: 30
    # 最大重试次数
    max-retry-count: 3
    
  inventory:
    # 库存锁定时间（分钟）
    lock-timeout-minutes: 35
    # 库存预警阈值
    warning-threshold: 10
    
  seckill:
    # 秒杀队列最大长度
    queue-max-length: 10000
    # 单用户最大秒杀次数
    max-seckill-count: 1
    # 秒杀开始前预热时间（分钟）
    preheat-minutes: 10

---
# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
    tags:
      application: mall-system
      
# 日志配置
logging:
  level:
    com.rabbitmq: DEBUG
    org.springframework.amqp: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%logger{50}] - %msg%n"
  file:
    name: logs/rabbitmq.log
    max-size: 100MB
    max-history: 30

---
# 集群配置（生产环境）
rabbitmq:
  cluster:
    nodes:
      - rabbit@node1
      - rabbit@node2  
      - rabbit@node3
    # 镜像队列配置
    ha-policy: all
    ha-sync-mode: automatic
    
  # 负载均衡配置
  load-balancer:
    addresses:
      - *************:5672
      - *************:5672
      - *************:5672
    # 连接策略
    address-shuffle-mode: random
    
  # 性能调优
  performance:
    # 连接池大小
    connection-pool-size: 10
    # 通道池大小
    channel-pool-size: 50
    # 心跳间隔（秒）
    heartbeat: 60
    # 网络恢复间隔（毫秒）
    network-recovery-interval: 5000
